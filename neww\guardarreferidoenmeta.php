// 1) AJAX handler para guardar coach_preferido
add_action('wp_ajax_as_save_coach', 'as_save_coach');
add_action('wp_ajax_nopriv_as_save_coach', 'as_save_coach');
function as_save_coach(){
    if (! is_user_logged_in()) {
        wp_send_json_error('not logged');
    }
    if ( empty($_POST['coach']) || ! ctype_digit($_POST['coach']) ) {
        wp_send_json_error('invalid coach');
    }
    $coach = intval($_POST['coach']);
    update_user_meta(get_current_user_id(), 'coach_preferido', $coach);
    wp_send_json_success('ok');
}

// 2) Al registrar un nuevo usuario, si viene cookie/session
add_action('user_register', 'as_assign_coach_on_register', 20);
function as_assign_coach_on_register($user_id){
    // 2.1) <PERSON>ie tiene prioridad
    if ( isset($_COOKIE['as_coach_ref']) && ctype_digit($_COOKIE['as_coach_ref']) ) {
        $coach = intval($_COOKIE['as_coach_ref']);
    }
    // 2.2) Luego session
    elseif ( session_status()===PHP_SESSION_ACTIVE
          && isset($_SESSION['as_coach_ref'])
          && ctype_digit($_SESSION['as_coach_ref']) ) {
        $coach = intval($_SESSION['as_coach_ref']);
    }
    else {
        return;
    }
    update_user_meta($user_id, 'coach_preferido', $coach);
    // (Opcional) limpiar
    if ( session_status()===PHP_SESSION_ACTIVE ) unset($_SESSION['as_coach_ref']);
    setcookie('as_coach_ref','', time()-3600, '/', COOKIE_DOMAIN);
}

// 3) Respaldo: si falla el JS, primera cita asigna coach
add_action('amelia_after_appointment_insert','as_set_coach_on_first_booking',10,2);
function as_set_coach_on_first_booking($appointmentId, $bookingId){
    $user = get_current_user_id();
    if (!$user) return;
    if ( get_user_meta($user,'coach_preferido',true) ) return;
    $prov = get_post_meta($appointmentId,'_providerId',true);
    if ($prov && ctype_digit((string)$prov)) {
        update_user_meta($user,'coach_preferido',intval($prov));
    }
}

// 4) Enqueue del parche JS **tras** el JS de Amelia
add_action('wp_enqueue_scripts','as_enqueue_coach_patch');
function as_enqueue_coach_patch(){
  global $post;
  if ( isset($post->post_content) && has_shortcode($post->post_content,'amelia') ){
    wp_enqueue_script('as-coach-patch',
      get_stylesheet_directory_uri().'/js/as-coach-patch.js',
      ['jquery','amelia-booking-js'],
      '1.0',
      true
    );
    wp_localize_script('as-coach-patch','asCoachVars',[
      'ajaxUrl'=>admin_url('admin-ajax.php')
    ]);
  }
}


