<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slideshow Tipo PowerPoint</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .slideshow-container {
            width: 90%;
            max-width: 1200px;
            height: 80vh;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            overflow: hidden;
            position: relative;
        }

        .content-area {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-right: 3px solid #333;
            position: relative;
        }

        .content-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .content-title {
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .content-text {
            font-size: 1.2em;
            line-height: 1.6;
            color: #555;
            text-align: center;
            margin-bottom: 20px;
        }

        .text-area {
            width: 100%;
            min-height: 200px;
            padding: 20px;
            border: none;
            border-radius: 15px;
            font-size: 1.1em;
            resize: vertical;
            font-family: inherit;
            background: white;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s;
            line-height: 1.6;
        }

        .text-area:focus {
            outline: none;
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .video-area {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: white;
        }

        .video-title {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }

        .video-subtitle {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 20px;
            text-align: center;
        }

        .video-container {
            width: 100%;
            height: 350px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 20px;
            position: relative;
            background: linear-gradient(45deg, #667eea, #764ba2);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .video-frame {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 15px;
        }

        .video-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
            color: white;
            font-size: 1.2em;
            text-align: center;
            border-radius: 15px;
        }

        .video-icon {
            font-size: 4em;
            margin-bottom: 15px;
            opacity: 0.7;
        }

        .video-input {
            width: 100%;
            padding: 12px 15px;
            border: none;
            border-radius: 25px;
            font-size: 0.9em;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            background: #f8f9fa;
        }

        .video-input:focus {
            outline: none;
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        .video-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 15px;
        }

        .load-video-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .load-video-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }

        .clear-video-btn {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.3);
        }

        .clear-video-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(244, 67, 54, 0.4);
        }

        .play-controls {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 10;
        }

        .play-btn {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .play-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
        }

        .volume-control {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }

        .volume-slider {
            width: 80px;
            margin-left: 10px;
            accent-color: #667eea;
        }

        .navigation {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-arrow {
            width: 60px;
            height: 60px;
            background: #333;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .nav-arrow:hover {
            background: #555;
            transform: scale(1.1);
        }

        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 1em;
        }

        .slide-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .slideshow-container {
                flex-direction: column;
                height: 95vh;
            }

            .content-area, .video-area {
                flex: none;
                height: 50%;
            }

            .content-area {
                border-right: none;
                border-bottom: 3px solid #333;
            }
        }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <div class="slide-indicator">
            Slide <span id="current-slide">1</span> de <span id="total-slides">3</span>
        </div>

        <div class="content-area">
            <div class="content-title">CONTENIDO</div>
            <div class="content-text">SE PUEDE AGREGAR TEXT AREA</div>
            <textarea class="text-area" id="slide-content" placeholder="Escribe aquí el contenido de tu slide...">Este es el contenido del slide actual. Puedes editarlo y agregar tu propio texto.</textarea>
        </div>

        <div class="video-area">
            <div class="video-title">VIDEO</div>
            <div class="video-subtitle">QUE VIENE DESDE DRIVE:</div>
            <div class="video-container">
                <iframe class="video-frame" id="video-frame" src="" allowfullscreen style="display: none;"></iframe>
                <div class="video-placeholder" id="video-placeholder">
                    <div class="video-icon">🎬</div>
                    <div>Agrega un video desde Google Drive</div>
                    <div style="font-size: 0.9em; opacity: 0.8; margin-top: 10px;">El video se reproducirá aquí</div>
                </div>
                <div class="play-controls" id="play-controls" style="display: none;">
                    <button class="play-btn" id="play-pause-btn" onclick="togglePlayPause()">▶️</button>
                    <div class="volume-control">
                        <span>🔊</span>
                        <input type="range" class="volume-slider" id="volume-slider" min="0" max="100" value="50">
                    </div>
                </div>
            </div>
            <input type="text" class="video-input" id="video-url" placeholder="Pega aquí tu URL de Google Drive...">
            <div class="video-controls">
                <button class="load-video-btn" onclick="loadVideo()">📹 Cargar Video</button>
                <button class="clear-video-btn" onclick="clearVideo()">🗑️ Limpiar</button>
            </div>
        </div>

        <div class="navigation">
            <button class="nav-arrow" id="prev-btn" onclick="previousSlide()">‹</button>
            <div class="slide-counter">
                <span id="slide-number">1</span> / <span id="slide-total">3</span>
            </div>
            <button class="nav-arrow" id="next-btn" onclick="nextSlide()">›</button>
        </div>
    </div>

    <script>
        // Datos de los slides
        let slides = [
            {
                title: "CONTENIDO",
                subtitle: "SE PUEDE AGREGAR TEXT AREA",
                content: "Este es el contenido del primer slide. Puedes editarlo y agregar tu propio texto.",
                videoUrl: ""
            },
            {
                title: "SEGUNDO SLIDE",
                subtitle: "OTRO EJEMPLO DE CONTENIDO",
                content: "Este es el contenido del segundo slide. Aquí puedes agregar información diferente.",
                videoUrl: "https://drive.google.com/file/d/1Yx0dNnswNPH-fuw6CfigXeusCFWW8cyQ/view?usp=sharing"
            },
            {
                title: "TERCER SLIDE",
                subtitle: "MÁS CONTENIDO AQUÍ",
                content: "Este es el contenido del tercer slide. Puedes seguir agregando más slides según necesites.",
                videoUrl: ""
            }
        ];

        let currentSlideIndex = 0;

        // Función para convertir URL de Google Drive a formato embebido
        function convertDriveUrl(url) {
            if (!url) return "";

            // Extraer el ID del archivo de la URL de Google Drive
            const match = url.match(/\/file\/d\/([a-zA-Z0-9-_]+)/);
            if (match) {
                const fileId = match[1];
                return `https://drive.google.com/file/d/${fileId}/preview`;
            }
            return url;
        }

        // Función para cargar un slide
        function loadSlide(index) {
            const slide = slides[index];

            // Actualizar contenido
            document.querySelector('.content-title').textContent = slide.title;
            document.querySelector('.content-text').textContent = slide.subtitle;
            document.getElementById('slide-content').value = slide.content;

            // Actualizar video
            const videoFrame = document.getElementById('video-frame');
            const videoInput = document.getElementById('video-url');
            const videoPlaceholder = document.getElementById('video-placeholder');
            const playControls = document.getElementById('play-controls');

            if (slide.videoUrl) {
                const embedUrl = convertDriveUrl(slide.videoUrl);
                videoFrame.src = embedUrl;
                videoInput.value = slide.videoUrl;
                videoFrame.style.display = 'block';
                videoPlaceholder.style.display = 'none';
                playControls.style.display = 'flex';
            } else {
                videoFrame.src = "";
                videoInput.value = "";
                videoFrame.style.display = 'none';
                videoPlaceholder.style.display = 'flex';
                playControls.style.display = 'none';
            }

            // Actualizar contadores
            document.getElementById('current-slide').textContent = index + 1;
            document.getElementById('slide-number').textContent = index + 1;
            document.getElementById('total-slides').textContent = slides.length;
            document.getElementById('slide-total').textContent = slides.length;

            // Actualizar botones de navegación
            document.getElementById('prev-btn').disabled = index === 0;
            document.getElementById('next-btn').disabled = index === slides.length - 1;
        }

        // Función para ir al slide anterior
        function previousSlide() {
            if (currentSlideIndex > 0) {
                // Guardar contenido actual antes de cambiar
                slides[currentSlideIndex].content = document.getElementById('slide-content').value;
                slides[currentSlideIndex].videoUrl = document.getElementById('video-url').value;

                currentSlideIndex--;
                loadSlide(currentSlideIndex);
            }
        }

        // Función para ir al siguiente slide
        function nextSlide() {
            if (currentSlideIndex < slides.length - 1) {
                // Guardar contenido actual antes de cambiar
                slides[currentSlideIndex].content = document.getElementById('slide-content').value;
                slides[currentSlideIndex].videoUrl = document.getElementById('video-url').value;

                currentSlideIndex++;
                loadSlide(currentSlideIndex);
            }
        }

        // Función para cargar video desde URL
        function loadVideo() {
            const videoUrl = document.getElementById('video-url').value.trim();
            const videoFrame = document.getElementById('video-frame');
            const videoPlaceholder = document.getElementById('video-placeholder');
            const playControls = document.getElementById('play-controls');

            if (videoUrl) {
                const embedUrl = convertDriveUrl(videoUrl);
                if (embedUrl) {
                    videoFrame.src = embedUrl;
                    videoFrame.style.display = 'block';
                    videoPlaceholder.style.display = 'none';
                    playControls.style.display = 'flex';

                    // Guardar en el slide actual
                    slides[currentSlideIndex].videoUrl = videoUrl;

                    // Mostrar mensaje de éxito
                    showNotification('✅ Video cargado correctamente', 'success');
                } else {
                    showNotification('❌ URL de Google Drive no válida', 'error');
                }
            } else {
                showNotification('⚠️ Por favor ingresa una URL', 'warning');
            }
        }

        // Función para limpiar video
        function clearVideo() {
            const videoFrame = document.getElementById('video-frame');
            const videoInput = document.getElementById('video-url');
            const videoPlaceholder = document.getElementById('video-placeholder');
            const playControls = document.getElementById('play-controls');

            videoFrame.src = "";
            videoInput.value = "";
            videoFrame.style.display = 'none';
            videoPlaceholder.style.display = 'flex';
            playControls.style.display = 'none';

            // Limpiar del slide actual
            slides[currentSlideIndex].videoUrl = "";

            showNotification('🗑️ Video eliminado', 'info');
        }

        // Función para controlar reproducción
        function togglePlayPause() {
            const videoFrame = document.getElementById('video-frame');
            const playBtn = document.getElementById('play-pause-btn');

            // Nota: Los iframes de Google Drive no permiten control directo
            // Esta función es más decorativa, pero se puede expandir
            if (playBtn.textContent === '▶️') {
                playBtn.textContent = '⏸️';
                showNotification('▶️ Reproduciendo...', 'info');
            } else {
                playBtn.textContent = '▶️';
                showNotification('⏸️ Pausado', 'info');
            }
        }

        // Función para mostrar notificaciones
        function showNotification(message, type = 'info') {
            // Crear elemento de notificación
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;

            // Estilos de la notificación
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 25px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                transform: translateX(100%);
                transition: all 0.3s ease;
                box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            `;

            // Colores según el tipo
            const colors = {
                success: 'linear-gradient(45deg, #4CAF50, #45a049)',
                error: 'linear-gradient(45deg, #f44336, #d32f2f)',
                warning: 'linear-gradient(45deg, #ff9800, #f57c00)',
                info: 'linear-gradient(45deg, #2196F3, #1976D2)'
            };

            notification.style.background = colors[type] || colors.info;

            // Agregar al DOM
            document.body.appendChild(notification);

            // Animar entrada
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remover después de 3 segundos
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Navegación con teclado
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowLeft') {
                previousSlide();
            } else if (event.key === 'ArrowRight') {
                nextSlide();
            }
        });

        // Cargar el primer slide al iniciar
        loadSlide(0);
    </script>
</body>
</html>