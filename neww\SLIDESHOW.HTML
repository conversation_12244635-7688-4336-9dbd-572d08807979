<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slideshow Tipo PowerPoint</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .slideshow-container {
            width: 90%;
            max-width: 1200px;
            height: 80vh;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            overflow: hidden;
            position: relative;
        }

        .content-area {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: #f8f9fa;
            border-right: 3px solid #333;
        }

        .content-title {
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .content-text {
            font-size: 1.2em;
            line-height: 1.6;
            color: #555;
            text-align: center;
            margin-bottom: 20px;
        }

        .text-area {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1.1em;
            resize: vertical;
            font-family: inherit;
        }

        .video-area {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: white;
        }

        .video-title {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }

        .video-subtitle {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 20px;
            text-align: center;
        }

        .video-container {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .video-frame {
            width: 100%;
            height: 100%;
            border: none;
        }

        .video-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .load-video-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s;
        }

        .load-video-btn:hover {
            background: #45a049;
        }

        .navigation {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-arrow {
            width: 60px;
            height: 60px;
            background: #333;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .nav-arrow:hover {
            background: #555;
            transform: scale(1.1);
        }

        .nav-arrow:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 1em;
        }

        .slide-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .slideshow-container {
                flex-direction: column;
                height: 95vh;
            }

            .content-area, .video-area {
                flex: none;
                height: 50%;
            }

            .content-area {
                border-right: none;
                border-bottom: 3px solid #333;
            }
        }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <div class="slide-indicator">
            Slide <span id="current-slide">1</span> de <span id="total-slides">3</span>
        </div>

        <div class="content-area">
            <div class="content-title">CONTENIDO</div>
            <div class="content-text">SE PUEDE AGREGAR TEXT AREA</div>
            <textarea class="text-area" id="slide-content" placeholder="Escribe aquí el contenido de tu slide...">Este es el contenido del slide actual. Puedes editarlo y agregar tu propio texto.</textarea>
        </div>

        <div class="video-area">
            <div class="video-title">VIDEO</div>
            <div class="video-subtitle">QUE VIENE DESDE DRIVE:</div>
            <div class="video-container">
                <iframe class="video-frame" id="video-frame" src="" allowfullscreen></iframe>
            </div>
            <input type="text" class="video-input" id="video-url" placeholder="EJEMPLO: https://drive.google.com/file/d/1Yx0dNnswNPH-fuw6CfigXeusCFWW8cyQ/view?usp=sharing">
            <button class="load-video-btn" onclick="loadVideo()">Cargar Video</button>
        </div>

        <div class="navigation">
            <button class="nav-arrow" id="prev-btn" onclick="previousSlide()">‹</button>
            <div class="slide-counter">
                <span id="slide-number">1</span> / <span id="slide-total">3</span>
            </div>
            <button class="nav-arrow" id="next-btn" onclick="nextSlide()">›</button>
        </div>
    </div>

    <script>
        // Datos de los slides
        let slides = [
            {
                title: "CONTENIDO",
                subtitle: "SE PUEDE AGREGAR TEXT AREA",
                content: "Este es el contenido del primer slide. Puedes editarlo y agregar tu propio texto.",
                videoUrl: ""
            },
            {
                title: "SEGUNDO SLIDE",
                subtitle: "OTRO EJEMPLO DE CONTENIDO",
                content: "Este es el contenido del segundo slide. Aquí puedes agregar información diferente.",
                videoUrl: "https://drive.google.com/file/d/1Yx0dNnswNPH-fuw6CfigXeusCFWW8cyQ/view?usp=sharing"
            },
            {
                title: "TERCER SLIDE",
                subtitle: "MÁS CONTENIDO AQUÍ",
                content: "Este es el contenido del tercer slide. Puedes seguir agregando más slides según necesites.",
                videoUrl: ""
            }
        ];

        let currentSlideIndex = 0;

        // Función para convertir URL de Google Drive a formato embebido
        function convertDriveUrl(url) {
            if (!url) return "";

            // Extraer el ID del archivo de la URL de Google Drive
            const match = url.match(/\/file\/d\/([a-zA-Z0-9-_]+)/);
            if (match) {
                const fileId = match[1];
                return `https://drive.google.com/file/d/${fileId}/preview`;
            }
            return url;
        }

        // Función para cargar un slide
        function loadSlide(index) {
            const slide = slides[index];

            // Actualizar contenido
            document.querySelector('.content-title').textContent = slide.title;
            document.querySelector('.content-text').textContent = slide.subtitle;
            document.getElementById('slide-content').value = slide.content;

            // Actualizar video
            const videoFrame = document.getElementById('video-frame');
            const videoInput = document.getElementById('video-url');

            if (slide.videoUrl) {
                const embedUrl = convertDriveUrl(slide.videoUrl);
                videoFrame.src = embedUrl;
                videoInput.value = slide.videoUrl;
            } else {
                videoFrame.src = "";
                videoInput.value = "";
            }

            // Actualizar contadores
            document.getElementById('current-slide').textContent = index + 1;
            document.getElementById('slide-number').textContent = index + 1;
            document.getElementById('total-slides').textContent = slides.length;
            document.getElementById('slide-total').textContent = slides.length;

            // Actualizar botones de navegación
            document.getElementById('prev-btn').disabled = index === 0;
            document.getElementById('next-btn').disabled = index === slides.length - 1;
        }

        // Función para ir al slide anterior
        function previousSlide() {
            if (currentSlideIndex > 0) {
                // Guardar contenido actual antes de cambiar
                slides[currentSlideIndex].content = document.getElementById('slide-content').value;
                slides[currentSlideIndex].videoUrl = document.getElementById('video-url').value;

                currentSlideIndex--;
                loadSlide(currentSlideIndex);
            }
        }

        // Función para ir al siguiente slide
        function nextSlide() {
            if (currentSlideIndex < slides.length - 1) {
                // Guardar contenido actual antes de cambiar
                slides[currentSlideIndex].content = document.getElementById('slide-content').value;
                slides[currentSlideIndex].videoUrl = document.getElementById('video-url').value;

                currentSlideIndex++;
                loadSlide(currentSlideIndex);
            }
        }

        // Función para cargar video desde URL
        function loadVideo() {
            const videoUrl = document.getElementById('video-url').value;
            const videoFrame = document.getElementById('video-frame');

            if (videoUrl) {
                const embedUrl = convertDriveUrl(videoUrl);
                videoFrame.src = embedUrl;

                // Guardar en el slide actual
                slides[currentSlideIndex].videoUrl = videoUrl;
            }
        }

        // Navegación con teclado
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowLeft') {
                previousSlide();
            } else if (event.key === 'ArrowRight') {
                nextSlide();
            }
        });

        // Cargar el primer slide al iniciar
        loadSlide(0);
    </script>
</body>
</html>