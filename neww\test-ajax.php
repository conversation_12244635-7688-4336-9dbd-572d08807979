<?php
/**
 * Script de prueba para verificar que el endpoint as_save_coach funcione
 * Ejecutar desde la línea de comandos o navegador
 */

// Simular una petición AJAX POST
$_POST['action'] = 'as_save_coach';
$_POST['coach_ref'] = 'coach_test_' . time();

// Incluir WordPress (ajustar la ruta según sea necesario)
// require_once('../wp-load.php');

echo "=== TEST AS_SAVE_COACH ENDPOINT ===\n";
echo "Datos de prueba:\n";
echo "- action: " . $_POST['action'] . "\n";
echo "- coach_ref: " . $_POST['coach_ref'] . "\n";
echo "\n";

// Verificar que la función existe
if (function_exists('as_save_coach_handler')) {
    echo "✅ Función as_save_coach_handler encontrada\n";
    
    // Capturar la salida
    ob_start();
    as_save_coach_handler();
    $output = ob_get_clean();
    
    echo "Respuesta del handler:\n";
    echo $output . "\n";
    
} else {
    echo "❌ Función as_save_coach_handler NO encontrada\n";
    echo "Asegúrate de que ameliasave.php esté incluido correctamente\n";
}

echo "\n=== VERIFICAR COOKIES ===\n";
if (isset($_COOKIE['as_coach_ref'])) {
    echo "✅ Cookie as_coach_ref encontrada: " . $_COOKIE['as_coach_ref'] . "\n";
} else {
    echo "ℹ️ Cookie as_coach_ref no encontrada (normal en CLI)\n";
}

echo "\n=== VERIFICAR HOOKS AJAX ===\n";
global $wp_filter;
if (isset($wp_filter['wp_ajax_as_save_coach'])) {
    echo "✅ Hook wp_ajax_as_save_coach registrado\n";
} else {
    echo "❌ Hook wp_ajax_as_save_coach NO registrado\n";
}

if (isset($wp_filter['wp_ajax_nopriv_as_save_coach'])) {
    echo "✅ Hook wp_ajax_nopriv_as_save_coach registrado\n";
} else {
    echo "❌ Hook wp_ajax_nopriv_as_save_coach NO registrado\n";
}

?>
