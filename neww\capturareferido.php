/**
 * 2.1) Iniciar sesión PHP lo antes posible
 */
add_action('init', 'as_init_session', 1);
function as_init_session() {
    if ( ! session_id() ) {
        session_start();
    }
}

/**
 * 2.2) Leer ref_id de la URL y guardarlo
 */
add_action('init', 'as_capture_referral');
function as_capture_referral() {
    // ¿Llegó un ref_id y es numérico?
    if ( isset($_GET['ref_id']) && ctype_digit($_GET['ref_id']) ) {
        $coach_ref = intval( $_GET['ref_id'] );

        // 1) Guardar en cookie (30 días)
        setcookie(
            'as_coach_ref',           // nombre
            $coach_ref,               // valor
            time() + 30 * DAY_IN_SECONDS,  // expiración
            COOKIEPATH,               // path
            COOKIE_DOMAIN             // dominio
        );

        // 2) Guardar en session (para acceso inmediato)
        $_SESSION['as_coach_ref'] = $coach_ref;
    }
}

