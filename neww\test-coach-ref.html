<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AS Coach Ref</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, button { padding: 8px; margin: 5px 0; }
        input[type="text"], input[type="number"] { width: 200px; }
        button { background: #0073aa; color: white; border: none; padding: 10px 15px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; background: #f9f9f9; }
        .success { border-color: #46b450; background: #ecf7ed; }
        .error { border-color: #dc3232; background: #fbeaea; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test AS Coach Ref Functionality</h1>
        
        <div class="form-group">
            <h3>1. Guardar Coach Reference</h3>
            <label for="coach_ref">Coach Reference:</label>
            <input type="text" id="coach_ref" placeholder="coach_123_1234567890">
            
            <label for="coach_id">Coach ID (opcional):</label>
            <input type="number" id="coach_id" placeholder="123">
            
            <button onclick="saveCoachRef()">Guardar Coach Ref</button>
        </div>
        
        <div class="form-group">
            <h3>2. Obtener Coach Reference</h3>
            <button onclick="getCoachRef()">Obtener Coach Ref</button>
        </div>
        
        <div class="form-group">
            <h3>3. Ver Cookie Actual</h3>
            <button onclick="showCurrentCookie()">Ver Cookie as_coach_ref</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // Configuración - CAMBIAR ESTA URL POR LA DE TU SITIO
        const AJAX_URL = 'https://tu-sitio.com/wp-admin/admin-ajax.php';
        
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.style.display = 'block';
        }
        
        function saveCoachRef() {
            const coachRef = document.getElementById('coach_ref').value;
            const coachId = document.getElementById('coach_id').value;
            
            if (!coachRef && !coachId) {
                showResult('Por favor ingresa al menos Coach Reference o Coach ID', false);
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'as_save_coach');
            if (coachRef) formData.append('coach_ref', coachRef);
            if (coachId) formData.append('coach_id', coachId);
            
            fetch(AJAX_URL, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(`✅ Coach Ref guardado exitosamente:<br>
                        <strong>Referencia:</strong> ${data.data.coach_ref}<br>
                        <strong>Expira:</strong> ${data.data.expires}`, true);
                } else {
                    showResult(`❌ Error: ${data.data}`, false);
                }
            })
            .catch(error => {
                showResult(`❌ Error de conexión: ${error.message}`, false);
            });
        }
        
        function getCoachRef() {
            const formData = new FormData();
            formData.append('action', 'as_get_coach_ref');
            
            fetch(AJAX_URL, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.data.found) {
                        showResult(`✅ Coach Ref encontrado:<br>
                            <strong>Referencia:</strong> ${data.data.coach_ref}`, true);
                    } else {
                        showResult(`ℹ️ ${data.data.message}`, true);
                    }
                } else {
                    showResult(`❌ Error: ${data.data}`, false);
                }
            })
            .catch(error => {
                showResult(`❌ Error de conexión: ${error.message}`, false);
            });
        }
        
        function showCurrentCookie() {
            const cookies = document.cookie.split(';');
            let coachRefCookie = null;
            
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'as_coach_ref') {
                    coachRefCookie = decodeURIComponent(value);
                    break;
                }
            }
            
            if (coachRefCookie) {
                showResult(`🍪 Cookie as_coach_ref encontrada:<br>
                    <strong>Valor:</strong> ${coachRefCookie}`, true);
            } else {
                showResult(`ℹ️ No se encontró la cookie as_coach_ref`, true);
            }
        }
        
        // Mostrar todas las cookies al cargar la página (para debugging)
        console.log('Todas las cookies:', document.cookie);
    </script>
</body>
</html>
