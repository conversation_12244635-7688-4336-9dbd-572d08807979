<?php
/**
 * Plugin Name: <PERSON> Save Coach Reference
 * Description: <PERSON><PERSON><PERSON> la funcionalidad de guardar referencias de coach en cookies después de appointments
 * Version: 1.0
 * Author: <PERSON>
 */

// Prevenir acceso directo
if (!defined('ABSPATH')) {
    exit;
}

add_action('rest_api_init', function () {

  // ——————————————————————————————————————————————
// 1) GET /wp-json/api/v1/students-coach
register_rest_route('api/v1', '/students-coach', [
  'methods'  => 'GET',
  'callback' => 'as_list_students_with_coach',
  'permission_callback' => '__return_true',
]);

  // ——————————————————————————————————————————————
  // 2) POST /wp-json/api/v1/student-coach
  //    Body JSON: { "id": 123, "coach_id": 456 }
  //    Actualiza el meta coach_preferido del estudiante
  register_rest_route('api/v1', '/student-coach', [
    'methods'             => 'POST',
    'callback'            => 'as_update_student_coach',
    'permission_callback' => '__return_true',
    'args'                => [
      'id' => [
        'required'          => true,
        'validate_callback' => function($v){ return ctype_digit((string)$v); }
      ],
      'coach_id' => [
        'required'          => true,
        'validate_callback' => function($v){ return ctype_digit((string)$v); }
      ],
    ],
  ]);

});

// ——————————————————————————————————————————————
// 3) AJAX Handler para guardar as_coach_ref como cookie
// Se ejecuta cuando se hace la petición AJAX as_save_coach
add_action('wp_ajax_as_save_coach', 'as_save_coach_handler');
add_action('wp_ajax_nopriv_as_save_coach', 'as_save_coach_handler');

function as_save_coach_handler() {
  // Verificar nonce si es necesario (recomendado para seguridad)
  // if (!wp_verify_nonce($_POST['nonce'], 'as_save_coach_nonce')) {
  //   wp_die('Security check failed');
  // }

  $coach_ref = isset($_POST['coach_ref']) ? sanitize_text_field($_POST['coach_ref']) : '';
  $coach_id = isset($_POST['coach_id']) ? intval($_POST['coach_id']) : 0;

  if (empty($coach_ref) && empty($coach_id)) {
    wp_send_json_error('No coach reference provided');
    return;
  }

  // Si no se proporciona coach_ref pero sí coach_id, usar el coach_id como referencia
  if (empty($coach_ref) && !empty($coach_id)) {
    $coach_ref = 'coach_' . $coach_id;
  }

  // Guardar la cookie as_coach_ref
  // La cookie expira en 30 días (30 * 24 * 60 * 60 = 2592000 segundos)
  $cookie_expiry = time() + (30 * 24 * 60 * 60);

  setcookie('as_coach_ref', $coach_ref, $cookie_expiry, '/');

  // También guardar en la sesión si está disponible
  if (session_status() == PHP_SESSION_NONE) {
    session_start();
  }
  $_SESSION['as_coach_ref'] = $coach_ref;

  // Respuesta exitosa
  wp_send_json_success([
    'message' => 'Coach reference saved successfully',
    'coach_ref' => $coach_ref,
    'expires' => date('Y-m-d H:i:s', $cookie_expiry)
  ]);
}

// ——————————————————————————————————————————————
// 4) Hook para capturar coach después del primer appointment
// Se ejecuta cuando se completa un appointment en Amelia
add_action('AmeliaBookingAdded', 'as_capture_coach_after_appointment', 10, 3);

function as_capture_coach_after_appointment($appointment, $booking, $container) {
  try {
    // Obtener el customer ID del booking
    $customer_id = isset($booking['customerId']) ? intval($booking['customerId']) : 0;
    $provider_id = isset($appointment['providerId']) ? intval($appointment['providerId']) : 0;

    if (!$customer_id || !$provider_id) {
      return;
    }

    // Verificar si es el primer appointment del customer
    global $wpdb;

    $appointment_count = $wpdb->get_var($wpdb->prepare("
      SELECT COUNT(*)
      FROM {$wpdb->prefix}amelia_customer_bookings cb
      JOIN {$wpdb->prefix}amelia_appointments a ON cb.appointmentId = a.id
      WHERE cb.customerId = %d AND a.status = 'approved'
    ", $customer_id));

    // Si es el primer appointment (count = 1, porque este ya se contó)
    if ($appointment_count == 1) {
      // Crear la referencia del coach
      $coach_ref = 'coach_' . $provider_id . '_' . time();

      // Guardar como cookie
      $cookie_expiry = time() + (30 * 24 * 60 * 60);
      setcookie('as_coach_ref', $coach_ref, $cookie_expiry, '/');

      // También guardar como meta del customer en Amelia
      $wpdb->insert(
        $wpdb->prefix . 'amelia_users_meta',
        [
          'userId' => $customer_id,
          'name' => 'as_coach_ref',
          'value' => $coach_ref
        ]
      );

      // Log para debugging (opcional)
      error_log("AS Coach Ref saved: $coach_ref for customer: $customer_id after first appointment");
    }

  } catch (Exception $e) {
    error_log("Error in as_capture_coach_after_appointment: " . $e->getMessage());
  }
}


function as_list_students_with_coach( \WP_REST_Request $req ) {
  global $wpdb;

  // 1-A) Listamos alumnos WP
  $students_wp = get_users([
    'role__in' => ['student','subscriber','customer'],
    'orderby'  => 'display_name',
  ]);
  $out_students = [];
  foreach ( $students_wp as $u ) {
    // siempre entero o null
    $meta = get_user_meta( $u->ID, 'coach_preferido', true );
    $coach_id = ( $meta !== '' && ctype_digit((string)$meta) )
              ? intval($meta)
              : null;

    $out_students[] = [
      'id'    => $u->ID,
      'name'  => $u->display_name,
      'email' => $u->user_email,
      'coach' => $coach_id,
    ];
  }

	
	/// ESTA PARTE SOLO ES PARA EDITAR
  // 1-B) Listamos **Amelia providers** por su tabla, no por WP roles
  $providers = $wpdb->get_results("
    SELECT id, firstName, lastName
      FROM qxq_amelia_users
     WHERE type = 'provider'
  ", ARRAY_A);

  $out_coaches = [];
  foreach( $providers as $p ){
    $id   = intval( $p['id'] );
    $name = trim( $p['firstName'].' '.$p['lastName'] );
    $out_coaches[ $id ] = $name;
  }

  return new WP_REST_Response([
    'students' => $out_students,
    'coaches'  => $out_coaches,
  ], 200 );
}





/**
 * 2) Callback para actualizar coach_preferido
 */
function as_update_student_coach( \WP_REST_Request $req ) {
  $body = json_decode( $req->get_body(), true );

  $user_id  = intval( $body['id'] );
  $coach_id = intval( $body['coach_id'] );

  if ( ! get_user_by( 'ID', $user_id ) ) {
    return new WP_REST_Response( ['error'=>'Estudiante no encontrado'], 404 );
  }

  // Guarda el meta
  update_user_meta( $user_id, 'coach_preferido', $coach_id );

  return new WP_REST_Response( ['success'=>true], 200 );
}

// ——————————————————————————————————————————————
// 5) Función helper para obtener as_coach_ref
function as_get_coach_ref() {
  // Primero intentar obtener de la cookie
  if (isset($_COOKIE['as_coach_ref']) && !empty($_COOKIE['as_coach_ref'])) {
    return sanitize_text_field($_COOKIE['as_coach_ref']);
  }

  // Si no está en cookie, intentar obtener de la sesión
  if (session_status() == PHP_SESSION_NONE) {
    session_start();
  }

  if (isset($_SESSION['as_coach_ref']) && !empty($_SESSION['as_coach_ref'])) {
    return sanitize_text_field($_SESSION['as_coach_ref']);
  }

  return null;
}

// ——————————————————————————————————————————————
// 6) AJAX Handler para obtener el coach_ref actual
add_action('wp_ajax_as_get_coach_ref', 'as_get_coach_ref_handler');
add_action('wp_ajax_nopriv_as_get_coach_ref', 'as_get_coach_ref_handler');

function as_get_coach_ref_handler() {
  $coach_ref = as_get_coach_ref();

  if ($coach_ref) {
    wp_send_json_success([
      'coach_ref' => $coach_ref,
      'found' => true
    ]);
  } else {
    wp_send_json_success([
      'coach_ref' => null,
      'found' => false,
      'message' => 'No coach reference found'
    ]);
  }
}
